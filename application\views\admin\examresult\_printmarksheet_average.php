<link rel="stylesheet" href="<?php echo base_url(); ?>backend/pdf_style.css?1">
<style type="text/css">
    @media print {
        .pagebreak {
            clear: both;
            page-break-after: always;
        }
        @page {
            size: A4;
            margin: 0;
        }
        .marksheet {
            margin: 0;
            border: initial;
            border-radius: initial;
            width: initial;
            min-height: initial;
            box-shadow: initial;
            background: initial;
            page-break-after: always;
        }
        .subject_name {
            background: #9a00ccb8 !important;
            color: #fff !important;
        }
        table.marks th {
            color: #fff !important;
            background-color: #2245ffcf !important;
        }
        .overall {
            background-color: #ffd3a0;
        }
        .overall_mark_details td:first-child,
        .overall_mark_details td:nth-child(3) {
            padding: 10px;
            background-color: #ffd3a0 !important;
            width: 20%;
            color: #fff !important;
        }
        .mark_range {
            background: #ffd3a0 !important;
            border-color: #000 !important;
            font-size: 10px;
        }
    }

    .mark_range {
        background: #ffd3a0 !important;
        border-color: #000 !important;
        font-size: 10px;
    }
    .subject_name {
        background: <?php echo $template->bgcolor != '#FFFFFF' ? $template->bgcolor : '#FFFFFF'; ?> !important;
        color: <?php echo $template->bgcolor != '#FFFFFF' ? '#fff' : '#000'; ?> !important;
    }
    table.marks th {
        color: #fff !important;
    }
    .overall {
        background-color: #ffd3a0;
    }
    .denifittable_marks table tr td {
        padding: 10px;
    }
    .overall_mark_details td:first-child {
        padding: 10px;
        background-color: #ffd3a0;
    } 
</style>

<?php
if (empty($marksheet)) {
    ?>
    <div class="alert alter-info">
        <?php echo $this->lang->line('no_record_found'); ?>
    </div>
    <?php
} else {
    // EXAM CONNECTION START
    if ($marksheet['exam_connection'] == 0) : 
        if (!empty($marksheet['students'])) {
            $total_students = count($marksheet['students']);
            $to_be_print = 0;
            $query = $this->db->get_where('set_marks_type', array(
                'school_id' => $this->school_id,
                'exam_id' => $exam_id,
                'exam_group_id' => $exam->exam_group_id,
                'status' => 1
            ));
            $rows = $query->result_array();
            
            foreach ($marksheet['students'] as $student_key => $student_value) :
                $to_be_print += 1;
                $result_status = 1;
                $absent_status = false;
                $percentage_total = 0;
                ?>
                <div class="marksheet">
                    <div style=" ">
                        
                        <!-- Header Image -->
                        <div style="margin-bottom: 10px;">
                            <?php if($template->header_image){ ?>
                                <img src="<?php echo $this->media_storage->getImageURL('uploads/marksheet/' . $template->header_image); ?>" width="100%" height="120px;">
                            <?php } ?>
                        </div>
                        
                        <div style="margin: 0 auto; width: 98%;"> 
                            <table cellpadding="0" cellspacing="0" width="100%">     
                                <!-- Exam Title -->
                                <tr>
                                    <td valign="top" style="font-size: 16px; font-weight: 500; text-align: center;">
                                        <div class="exam-title"><?php echo $exam->exam; ?> - <?php if($template->exam_session) { ?>
                                        <?php echo $exam->session; ?>
                                        <?php } ?></div>  
                                    </td>
                                </tr>
                                
                                <!-- Student Information -->
                                <tr style="margin-top:10px;">
                                    <td>
                                        <table class="table table-bordered">
                                            <tr>
                                                <?php if($template->is_name) { ?>
                                                <td>
                                                    <div>
                                                        <?php echo $this->lang->line('name_prefix'); ?> :
                                                        <span style="font-weight: 500;" class="span">
                                                            <?php echo $this->customlib->getFullName($student_value['firstname'],$student_value['middlename'],$student_value['lastname'],$sch_setting->middlename,$sch_setting->lastname); ?>
                                                        </span>
                                                    </div>
                                                </td>
                                                <?php } ?>
                                                <?php if($template->is_class) { ?>
                                                <td>
                                                    <div>
                                                        <?php echo $this->lang->line('class'); ?> :
                                                        <span style="font-weight: 500;" class="span">
                                                            <?php 
																echo $student_value['class']; 
																if($template->is_section) {  
																	echo " (" . $student_value['section'] . ")";  
																} 
															?>
                                                        </span>
                                                    </div>
                                                </td>
                                                <?php } ?>
                                                <td rowspan="3" style="text-align:center;">
                                                    <?php
                                                    if ($template->is_photo) {
                                                        if ($student_value['image'] != '') { 
                                                            ?>
                                                            <img style="" src="<?php echo $this->media_storage->getImageURL($student_value['image']); ?>" width="90" height="100">
                                                            <?php 
                                                        } else {
                                                            if ($student_value['gender'] == 'Female') {
                                                                $image = $this->media_storage->getImageURL("uploads/student_images/default_female.jpg");
                                                            } elseif ($student_value['gender'] == 'Male') {
                                                                $image = $this->media_storage->getImageURL("uploads/student_images/default_male.jpg");
                                                            }
                                                            ?>
                                                            <img style="" src="<?php echo $image; ?>" width="90" height="100">
                                                            <?php 
                                                        }
                                                    }
                                                    ?>
                                                </td> 
                                            </tr>
                                            <tr>
                                                <td>
                                                    <div>
                                                        <?php if ($template->is_father_name) { ?>
                                                            Father's Name : <span style="font-weight: 500;"><?php echo $student_value['father_name']; ?></span>
                                                        <?php } ?>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div>
                                                        <?php if($template->is_mother_name) { ?>
                                                            Mother's Name : <span style="font-weight: 500;"><?php echo $student_value['mother_name']; ?></span>
                                                        <?php } ?>
                                                    </div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td valign="top" style="text-transform: capitalize; font-size: 14px;">
                                                    <div>
                                                        <?php if($template->is_roll_no) { ?>
                                                        Roll no <?php if($template->is_admission_no) { ?>/ ADM No.<?php } ?> :
                                                        <span style="font-weight: 500;">
                                                            <?php if($template->is_roll_no) { echo $student_value['student_roll_no']; } ?>
                                                            <?php if($template->is_roll_no && $template->is_admission_no) { echo ' / '; } ?>
                                                            <?php if($template->is_admission_no) { echo $student_value['admission_no']; } ?>
                                                        </span>
                                                        <?php } ?>
                                                    </div>
                                                </td>
                                                <td>
                                                    <div>
                                                        <?php if($template->is_dob) { ?>
                                                        DOB : <span style="font-weight: 500;"><?php echo $this->customlib->dateformat($student_value['dob']); ?></span>
                                                        <?php } ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        </table>
                                    </td>
                                </tr>
                                
                                <!-- Marks Table -->
                                <tr>
                                    <td valign="top">
                                        <table width="96%" cellpadding="0" cellspacing="0" class="denifittable marks" style="text-align: center; text-transform: uppercase;margin: 0 auto;">
                                            <col width="100"><col width="100"><col width="100">
                                            <tbody>
                                                <tr style="font-size:10px;font-weight: 400;">
                                                    <th><?php echo $this->lang->line('subjects') ?></th>
                                                        <?php if($template->is_show_max_marks) { ?>
                                                        <th style="width:120px"><?php echo $this->lang->line('max_marks') ?></th>
                                                        <?php } ?>
                                                        <?php if($query->num_rows() != 0) :
                                                            foreach($rows as $value) :
                                                                ?>
                                                                <th style="width:120px"><?php echo $value['name']; ?></th>
                                                            <?php endforeach; endif; ?>
                                                        <th style="width:120px">Mark Obtn.</th>
                                                        <?php if($template->is_grade) { ?>
                                                        <th style="width:55px"><?php echo $this->lang->line('grade'); ?></th>
                                                        <?php } ?>
														<?php if($template->is_note) { ?>  
														<th valign="top" style="text-align: left;border-right:1px solid #999;max-width:50px;width:15%">
															<?php echo $this->lang->line('note') ?>
														</th>	
														<?php } ?>
                                                </tr>
                                                
                                                <?php
                                                $total_max_marks = 0;
                                                $total_obtain_marks = 0;
                                                $total_points = 0;
                                                $total_hours = 0;
                                                $total_quality_point = 0;
                                                $grade_type = 0;
                                                
                                                foreach ($student_value['exam_result'] as $exam_result_key => $exam_result_value) {
                                                    if ($exam_result_value->exam_type == "number") {
                                                        $total_max_marks = $total_max_marks + $exam_result_value->max_marks;
                                                        $total_obtain_marks = $total_obtain_marks + $exam_result_value->get_marks;
                                                        ?>
                                                        <tr>
                                                            <td class="subject_name"><?php echo $exam_result_value->name; ?></td>
                                                            
                                                                <?php if($template->is_show_max_marks) { ?>
                                                                <td class="subject_name"><?php echo $exam_result_value->max_marks; ?></td>
                                                                <?php } ?>
                                                                <?php if($query->num_rows() != 0) :
                                                                    foreach($rows as $value) :
                                                                        $exam_type_name = $value['mark_name'];
                                                                        ?>
                                                                        <td class="subject_name" style="width:85px"><?php echo $exam_result_value->$exam_type_name; ?></td>
                                                                    <?php endforeach; endif; ?>
                                                                <td class="subject_name">
                                                                    <?php
                                                                    echo $exam_result_value->get_marks;
                                                                    if ($exam_result_value->attendence == "absent") {
                                                                        echo "&nbsp;" . $this->lang->line('exam_absent');
                                                                        $absent_status = true;
                                                                    }
                                                                    if ($exam_result_value->get_marks < $exam_result_value->min_marks) {
                                                                        $result_status = 0;
                                                                    }
                                                                    ?>
                                                                </td>
                                                                <?php if($template->is_grade) { ?>
                                                                <td class="subject_name" style="border-right:0">
                                                                    <?php
                                                                    $percentage_grade = ($exam_result_value->get_marks * 100) / $exam_result_value->max_marks;
                                                                    echo findGrade($exam_grades, $percentage_grade);
                                                                    ?>
                                                                </td>
                                                                <?php } ?>
                                                            
                                                            <?php if($template->is_note) { ?>  
                                                            <td class="subject_name" valign="top" style="text-align: left;border-right:1px solid #999">
                                                                <?php 
                                                                if ($exam_result_value->get_marks < $exam_result_value->min_marks) {
                                                                    $result_status = 0;
                                                                    echo "FAIL";
                                                                } elseif($exam_result_value->note == '') { 
                                                                    echo findGradeDescription($exam_grades, $percentage_grade); 
                                                                } elseif($exam_result_value->note != '') {
                                                                    echo $exam_result_value->note;
                                                                }
                                                                ?>
                                                            </td>
															<?php } ?>
															
                                                        </tr>
                                                        <?php
                                                    } else {
                                                        $grade_type++;
                                                    }
                                                }   
                                                ?>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr> 
                            </table>
                            
                            <!-- Overall Marks Summary -->
                            <table class="table-bordered denifittable_marks" cellpadding="0" cellspacing="0" width="100%" style="text-align: center;margin-top: 20px; text-transform: uppercase;">
                                <tr class="overall_mark_details">
                                     
                                    <td style="font-size:14px">Total Marks</td>
                                    <td><b><?php echo $total_max_marks; ?></b></td> 
                                    <td style="" class="text-center">Percentage</td>
                                    <td>
                                        <?php
                                        $percentage_grade = ($total_obtain_marks * 100) / $total_max_marks;
                                        echo round($percentage_grade, 2).'%';
                                        ?>
                                    </td>
                                </tr>
                                <tr class="overall_mark_details">
                                    <td class="text-center"><?php echo $this->lang->line('marks_obtained') ?></td>
                                    <td><b><?php echo number_format($total_obtain_marks, 0, '.', ''); ?></b></td>
                                    <?php if($template->is_grade) { ?>
                                    <td>Overall Grade</td>
                                    <td class="text-center">
                                        <?php
                                        $overall_percentage = ($total_obtain_marks * 100) / $total_max_marks;
                                        echo findGrade($exam_grades, $overall_percentage);
                                        ?>
                                    </td>
                                    <?php } ?>
                                </tr>
                                <?php if($template->is_rank) { ?>
                                <tr class="overall_mark_details">
                                    <td>Rank</td>
                                    <td class="text-center" colspan="<?php echo ($template->is_show_max_marks && $template->is_grade) ? '3' : (($template->is_show_max_marks || $template->is_grade) ? '2' : '1'); ?>">
                                        <?php echo ($student_value['rank']); ?>
                                    </td>
                                </tr>
                                <?php } ?>
                            </table>
                            
                            <!-- Co-Scholastic Section -->
                            <?php if($grade_type != '0') : ?>
                                <table class="table table-bordered font-size: 14px;">
                                    <tr>
                                        <th colspan="" style="background: #bdd7ff;font-size: 10px;">
                                            CO-SCHOLASTIC : (3 POINT GRADING SCALE A , B , C)
                                        </th> 
                                        <th>GRADE</th>
                                        <th colspan="" style="background: #bdd7ff;font-size: 10px;">
                                            CO-SCHOLASTIC : (3 POINT GRADING SCALE A , B , C)
                                        </th> 
                                        <th>GRADE</th>
                                    </tr>
                                    <?php 
                                    $i = 1;
                                    foreach ($student_value['exam_result'] as $exam_result_key => $exam_result_value) : 
                                        if ($exam_result_value->exam_type != "number") : 
                                            if($i % 2 == 1) :
                                                echo "<tr>"; 
                                            endif;
                                            ?>
                                            <td><?php echo $exam_result_value->name; ?></td>
                                            <td><?php echo $exam_result_value->grade; ?></td>
                                            <?php
                                            if($i % 2 == 0) :
                                                echo "</tr>";
                                            endif;
                                            $i++; 
                                        endif; 
                                    endforeach; 
                                    ?>
                                </table>  
                            <?php endif; ?> 
                            
                            <!-- Signature Section -->
                            <table class="table table-bordered" valign="top" cellpadding="0" cellspacing="0" style="margin-top: 20px;">
                                <tr>
									<?php if($template->is_show_date) { ?>
                                    <td width="20%" style="margin:0 auto;vertical-align: middle;" rowspan="2">
                                        <div style="font-size:20px;font-weight: 600;text-transform: uppercase; border-bottom: 1px solid;width: 200px;text-align: center;">DATE</div>
                                        <div style="width:200px;text-align: center;">
                                            <?php echo ($template->date != "") ? $template->date : date('d-m-Y'); ?>
                                        </div>
                                    </td>
                                    <td style="height:45px" valign="bottom">
                                        <?php if ($template->left_sign != "") { ?>
                                        <img src="<?php echo $this->media_storage->getImageURL('uploads/marksheet/' . $template->left_sign); ?>" width="200" height="50">
                                        <?php } ?>
                                    </td>
                                    <td style="height:45px" valign="bottom">
                                        <?php if ($template->middle_sign != "") { ?>
                                            <img src="<?php echo $this->media_storage->getImageURL('uploads/marksheet/' . $template->middle_sign); ?>" width="200" height="50">
                                        <?php } ?>
                                    </td>
                                    <?php if ($template->right_sign != "") { ?>
                                    <td style="height:45px" valign="bottom">
                                        <img src="<?php echo $this->media_storage->getImageURL('uploads/marksheet/' . $template->right_sign); ?>" width="200" height="50">
                                    </td>
                                    <?php } ?>
                                </tr>
                                <tr>
                                    <td style="font-size:12px">SIGNATURE OF CLASS TEACHER</td>
                                    <td style="font-size:12px">SIGNATURE OF PRINCIPAL</td>
                                    <?php if ($template->right_sign != "") { ?>
                                    <td style="font-size:12px">EXAM CONTROLLER</td>
                                    <?php } ?>
                                </tr>
                                <?php if ($template->content_footer != "") { ?>
                                    <tr>
                                       <td valign="bottom" style="font-size: 12px;">
                                           <?php echo $template->content_footer ?>
                                       </td>
                                   </tr>
                                <?php } ?>  
                            </table>
                            
                            <!-- Grade Range Table -->
                            <table class="table table-bordered" cellpadding="0" cellspacing="0">
                                <tr>
                                    <th class="mark_range">Mark Range In (%)</th>
                                    <?php foreach ($exam_grades as $grade): ?>
                                        <td style="font-size:11px"><?php echo $grade->mark_upto .' - '. $grade->mark_from; ?></td>
                                    <?php endforeach; ?>
                                </tr>
                                <tr>
                                    <th class="mark_range">Grade</th>
                                    <?php foreach ($exam_grades as $grade): ?>
                                        <td style="font-size:14px"><?php echo $grade->name; ?></td>
                                    <?php endforeach; ?>
                                </tr>
                            </table>
                        </div>
                    </div>
                </div>
                <?php 
            endforeach; 
        }
    // EXAM CONNECTION END
    endif;
}

// Helper Functions
function findExamDivision($marks_division, $percentage) {  
    if (!empty($marks_division)) {
        foreach ($marks_division as $division_key => $division_value) {
            if ($division_value->percentage_from >= $percentage && $division_value->percentage_to <= $percentage) {
                return $division_value->name;
            }
        }
    }       
    return "";
}

 

function findGradePoints($exam_grades, $percentage) {
    if (!empty($exam_grades)) {
        foreach ($exam_grades as $exam_grade_key => $exam_grade_value) {
            if ($exam_grade_value->mark_from >= $percentage && $exam_grade_value->mark_upto <= $percentage) {
                return $exam_grade_value->point;
            }
        }
    }
    return 0;
}

function examTotalResult($array, $exam_type, $exam_grade) {
    $return_array = array('max_marks' => 0, 'min_marks' => 0, 'credit_hours' => 0, 'get_marks' => 0, 'exam_result' => true);

    if (!empty($array)) {
        $max_marks = 0;
        $min_marks = 0;
        $credit_hours = 0;
        $get_marks = 0;
        $exam_result = true;
        $total_points = 0;
        $quality_point = 0;
        
        foreach ($array as $array_key => $array_value) {
            if ($array_value->attendence == "absent") {
                $exam_result = false;
            }

            $percentage_grade = ($array_value->get_marks * 100) / $array_value->max_marks;
            $point = findGradePoints($exam_grade, $percentage_grade);
            $total_points += $point;
            $quality_point += ($point * $array_value->credit_hours);
            
            $max_marks = $max_marks + $array_value->max_marks;
            $min_marks = $min_marks + $array_value->min_marks;
            $credit_hours = $credit_hours + $array_value->credit_hours;
            $get_marks = $get_marks + $array_value->get_marks;
        }

        $return_array = array(
            'max_marks' => $max_marks, 
            'min_marks' => $min_marks, 
            'credit_hours' => $credit_hours, 
            'get_marks' => $get_marks, 
            'exam_result' => $exam_result,
            'total_points' => $total_points,
            'quality_point' => $quality_point
        );
    }
    return json_encode($return_array);
}

function getWeightageExam($exam_connection_list, $examid, $get_marks, $exam_get_percentage) {
    foreach ($exam_connection_list as $exam_connection_key => $exam_connection_value) {
        if ($exam_connection_value->exam_group_class_batch_exams_id == $examid) {
            return [
                'exam_weightage' => $exam_connection_value->exam_weightage,
                'exam_consolidate_marks' => ($get_marks * $exam_connection_value->exam_weightage) / 100,
                'exam_consolidate_percentage' => ($exam_get_percentage * $exam_connection_value->exam_weightage) / 100
            ];
        }
    }
    return "";
}
?>