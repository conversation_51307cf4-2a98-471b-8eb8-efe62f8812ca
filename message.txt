
Select name from employee group by name having count(*)>1;

===========================================================================================

New school onboard k time humko notification setting insert krna parega

Table Insert Onboarding time

emailconfig
notification setting 
fee reminder


ALTER TABLE `sch_settings` ADD `id` INT NOT NULL AUTO_INCREMENT FIRST, ADD PRIMARY KEY (`id`);
ALTER TABLE `class_sections` ADD `school_id` INT NOT NULL AFTER `id`;
ALTER TABLE `roles_permissions` ADD `school_id` INT NOT NULL AFTER `id`;
ALTER TABLE `grades` ADD `school_id` INT NOT NULL AFTER `id`;
ALTER TABLE `notification_setting` ADD `school_id` INT NOT NULL AFTER `id`;
ALTER TABLE `disable_reason` ADD `school_id` INT NOT NULL AFTER `id`;

ALTER TABLE `captcha` ADD `school_id` INT NOT NULL AFTER `id`;
ALTER TABLE `complaint_type` ADD `school_id` INT NOT NULL AFTER `id`;
ALTER TABLE `grades` ADD `school_id` INT NOT NULL AFTER `id`;
ALTER TABLE `template_admitcards` ADD `school_id` INT NOT NULL AFTER `id`;
ALTER TABLE `template_marksheets` ADD `school_id` INT NOT NULL AFTER `id`;
ALTER TABLE `visitors_purpose` ADD `school_id` INT NOT NULL AFTER `id`;

ALTER TABLE `exam_group_class_batch_exam_subjects` ADD `exam_type` VARCHAR(255) NOT NULL AFTER `subject_id`;
ALTER TABLE `exam_group_class_batch_exam_subjects` ADD `exam_sitting` INT NOT NULL AFTER `exam_type`;

ALTER TABLE `exam_group_exam_results` CHANGE `get_marks` `get_marks` VARCHAR(255) NULL DEFAULT '0.00';




++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++


ALTER TABLE `template_admitcards` ADD `header_image` VARCHAR(255) NOT NULL AFTER `background_img`;



ALTER TABLE `users` ADD `school_id` INT NOT NULL AFTER `id`;

ALTER TABLE `staff_attendance` ADD `punchtime` VARCHAR(255)  NULL AFTER `date`;

ALTER TABLE `student_attendences` ADD `punchtime` VARCHAR(255)  NULL AFTER `date`;

ALTER TABLE `fee_groups_feetype` ADD `month` VARCHAR(255) NOT NULL AFTER `session_id`;

ALTER TABLE `sch_settings` ADD `qr_code_img` VARCHAR(255) NOT NULL AFTER `offline_bank_payment_instruction`;
ALTER TABLE `sch_settings` ADD `bank_account` VARCHAR(255) NOT NULL AFTER `offline_bank_payment_instruction`;

-- Marksheet template updates
ALTER TABLE `template_marksheets` ADD `is_grade` TINYINT(1) NOT NULL DEFAULT '0' AFTER `is_teacher_remark`;
ALTER TABLE `template_marksheets` ADD `is_show_max_marks` TINYINT(1) NOT NULL DEFAULT '1' AFTER `is_grade`; 
ALTER TABLE `template_marksheets` ADD `is_note` TINYINT(1)  NOT NULL DEFAULT '1' AFTER `is_show_max_marks`;
-- Remove logo and exam fields from template_marksheets
ALTER TABLE `template_marksheets` DROP COLUMN `left_logo`;
ALTER TABLE `template_marksheets` DROP COLUMN `right_logo`; 
ALTER TABLE `template_marksheets` DROP COLUMN `background_img`;
ALTER TABLE `template_marksheets` DROP COLUMN `exam_name`;
ALTER TABLE `template_marksheets` DROP COLUMN `school_name`;
ALTER TABLE `template_marksheets` DROP COLUMN `exam_center`;

ALTER TABLE `student_session` ADD `hostel_id` INT NOT NULL AFTER `section_id`, ADD `hostel_fee` VARCHAR(255) NOT NULL AFTER `hostel_id`;

ALTER TABLE `student_fees_deposite` ADD `student_hostel_fee_id` INT  NULL AFTER `student_transport_fee_id`;
INSERT INTO `sidebar_sub_menus` (`id`, `sidebar_menu_id`, `menu`, `key`, `lang_key`, `url`, `level`, `access_permissions`, `permission_group_id`, `activate_controller`, `activate_methods`, `addon_permission`, `is_active`, `created_at`) VALUES (NULL, '21', 'fees_master', NULL, 'fees_master', 'admin/hostel/feemaster', '1', '(\'transport_fees_master\', \'can_view\')', NULL, 'hostel', 'feemaster', '', '1', '2023-03-31 11:03:14');


++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++

ALTER TABLE `class_section_times` ADD `exit_time` TIME  NULL AFTER `time`;
ALTER TABLE `class_section_times` ADD `school_id` INT NOT NULL AFTER `id`;



+++++++++++++++++++

ALTER TABLE `exam_group_exam_results` ADD `school_id` INT NOT NULL AFTER `id`;


====================================================

ALTER TABLE `student_hostel_fees` ADD `total_fees_fine_amount` VARCHAR(255)  NOT NULL DEFAULT '0' AFTER `hostel_room_id`;


ALTER TABLE `student_transport_fees` ADD `total_fees_fine_amount` VARCHAR(255)  NOT NULL DEFAULT '0' AFTER `route_pickup_point_id`;

ALTER TABLE `fee_groups_feetype` ADD `total_fees_fine_amount` VARCHAR(255)  NOT NULL DEFAULT '0' AFTER `fine_amount`;



ALTER TABLE `student_hostel_fees` ADD `total_fees_discount_amount` VARCHAR(255) NOT NULL DEFAULT '0' AFTER `total_fees_fine_amount`;


ALTER TABLE `student_transport_fees` ADD `total_fees_discount_amount` VARCHAR(255)   NOT NULL DEFAULT '0' AFTER `total_fees_fine_amount`;
ALTER TABLE `fee_groups_feetype` ADD `total_fees_discount_amount` VARCHAR(255)   NOT NULL DEFAULT '0'  AFTER `total_fees_fine_amount`;


ALTER TABLE `fees_discounts` ADD `fee_type` VARCHAR(255) NOT NULL AFTER `code`;



=======================================
ALTER TABLE `fees_discounts` ADD `fee_category` VARCHAR(255) NOT NULL AFTER `code`;

ALTER TABLE `messages` ADD `file` VARCHAR(255) NOT NULL AFTER `message`;



INSERT INTO `sidebar_sub_menus` (`id`, `sidebar_menu_id`, `menu`, `key`, `lang_key`, `url`, `level`, `access_permissions`, `permission_group_id`, `activate_controller`, `activate_methods`, `addon_permission`, `is_active`, `created_at`) VALUES (NULL, '2', 'student_image_upload', NULL, 'student_image_upload', 'student/student_image_upload', '9', '(\'student_image_upload\', \'can_view\')', NULL, 'student', 'index,edit', NULL, '1', '2022-07-23 12:20:41');

ALTER TABLE `student_attendences` ADD `exittime` VARCHAR(255) NOT NULL AFTER `punchtime`;



ALTER TABLE `student_transport_fees` ADD `school_id` INT NOT NULL AFTER `id`;
ALTER TABLE `student_hostel_fees` ADD `school_id` INT NOT NULL AFTER `id`;



=====================================================================================

ALTER TABLE `front_cms_pages` ADD `page_image_1` VARCHAR(255) NOT NULL AFTER `feature_image`, ADD `page_image_2` VARCHAR(255) NOT NULL AFTER `page_image_1`, ADD `page_image_3` VARCHAR(255) NOT NULL AFTER `page_image_2`, ADD `page_image_4` VARCHAR(255) NOT NULL AFTER `page_image_3`;

ALTER TABLE `subject_group_class_sections` ADD `school_id` INT NOT NULL AFTER `id`;

ALTER TABLE `staff_rating` ADD `school_id` INT NOT NULL AFTER `id`;

===========================================================================================

INSERT INTO `sidebar_sub_menus` (`id`, `sidebar_menu_id`, `menu`, `key`, `lang_key`, `url`, `level`, `access_permissions`, `permission_group_id`, `activate_controller`, `activate_methods`, `addon_permission`, `is_active`, `created_at`) VALUES (NULL, '3', 'student_fee_master', NULL, 'student_fee_master', 'student_fee_master/', '10', '(\'student_fee_master\', \'can_view\')', NULL, 'student_fee_master', 'index', '', '1', '2022-08-08 11:33:21');



ALTER TABLE `student_hostel_fees` ADD `fee_amount` FLOAT(10,2) NOT NULL DEFAULT '0.00' AFTER `hostel_room_id`;


ALTER TABLE `student_transport_fees` ADD `fee_amount` FLOAT(10,2) NOT NULL DEFAULT '0.00' AFTER `route_pickup_point_id`;

ALTER TABLE `student_applyleave` ADD `subject` TEXT NOT NULL AFTER `student_session_id`;




============================================
ALTER TABLE `students` ADD `access_key` TEXT NOT NULL AFTER `dis_note`;



ALTER TABLE `student_fees_deposite` ADD `reference_id` VARCHAR(255) NOT NULL  AFTER `is_active`;

ALTER TABLE `student_fee_inv` ADD `reference_id` VARCHAR(255) NOT NULL AFTER `print_details`;

ALTER TABLE `student_fee_inv` ADD `cancelled` INT NOT NULL AFTER `reference_id`;
ALTER TABLE `student_fee_inv` CHANGE `cancelled` `cancelled` INT NOT NULL DEFAULT '0';



==================================================================

ALTER TABLE `sch_settings` ADD `school_timming` VARCHAR(255) NOT NULL AFTER `name`;
ALTER TABLE `staff_attendance` ADD `exittime` VARCHAR(255)  NULL AFTER `punchtime`;


ALTER TABLE `exams` ADD `school_id` INT NOT NULL AFTER `id`;



#REMOVE DUPLICATES

ALTER IGNORE TABLE `student_attendences` ADD UNIQUE (`school_id`, `student_session_id`,`date`);



=======================================================

INSERT INTO `sidebar_sub_menus` (`id`, `sidebar_menu_id`, `menu`, `key`, `lang_key`, `url`, `level`, `access_permissions`, `permission_group_id`, `activate_controller`, `activate_methods`, `addon_permission`, `is_active`, `created_at`) VALUES (NULL, '11', 'monthly_test', NULL, 'monthly_test', 'admin/monthly_test', '11', '(\'monthly_test\', \'can_view\')', NULL, 'monthly_test', 'index,edit', '', '1', '2022-08-25 11:34:26');


INSERT INTO `permission_group` (`id`, `name`, `short_code`, `is_active`, `system`, `created_at`) VALUES (NULL, 'Academic Test', 'academic_performance', '1', '0', '2019-11-27 17:44:14');

ALTER TABLE `student_fee_inv` ADD `payment_mode` VARCHAR(255) NOT NULL AFTER `print_details`;

ALTER TABLE `student_fee_inv` ADD `collected_by` VARCHAR(255) NOT NULL AFTER `payment_mode`;