<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Word of Day Model
 * 
 * Handles database operations for word of the day feature
 */
class Word_of_day_model extends MY_Model
{
    protected $current_session;
    protected $school_id;

    public function __construct()
    {
        parent::__construct();
        $this->current_session = $this->setting_model->getCurrentSession();
        $this->school_id = $this->session->userdata('school_id');
    }

    /**
     * Get word of the day settings for a school
     */
    public function getSettings($school_id = null)
    {
        if ($school_id === null) {
            $school_id = $this->school_id;
        }

        $this->db->where('school_id', $school_id);
        $query = $this->db->get('word_of_day_settings');
        
        if ($query->num_rows() > 0) {
            return $query->row();
        }
        
        // Return default settings if none exist
        return $this->createDefaultSettings($school_id);
    }

    /**
     * Create default settings for a school
     */
    private function createDefaultSettings($school_id)
    {
        $default_settings = [
            'school_id' => $school_id,
            'is_enabled' => 1,
            'send_notice' => 1,
            'send_push' => 1,
            'delivery_time' => '06:00:00',
            'notice_template' => '🌟 <strong>Word of the Day</strong> 🌟<br><br><strong>{title}</strong><br><br>{content}<br><br>Have a wonderful day ahead!<br><br>- {school_name}',
            'push_template' => '🌟 {title} - {content}'
        ];
        
        $this->db->insert('word_of_day_settings', $default_settings);
        $insert_id = $this->db->insert_id();
        
        if ($insert_id) {
            $default_settings['id'] = $insert_id;
            return (object) $default_settings;
        }
        
        return null;
    }

    /**
     * Update word of the day settings
     */
    public function updateSettings($school_id, $data)
    {
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        $this->db->where('school_id', $school_id);
        $query = $this->db->get('word_of_day_settings');
        
        if ($query->num_rows() > 0) {
            // Update existing settings
            $this->db->where('school_id', $school_id);
            return $this->db->update('word_of_day_settings', $data);
        } else {
            // Insert new settings
            $data['school_id'] = $school_id;
            return $this->db->insert('word_of_day_settings', $data);
        }
    }

    /**
     * Get content by date
     */
    public function getContentByDate($school_id, $date)
    {
        $this->db->where('school_id', $school_id);
        $this->db->where('content_date', $date);
        $query = $this->db->get('word_of_day_content');
        
        return $query->num_rows() > 0 ? $query->row() : null;
    }

    /**
     * Save word of the day content
     */
    public function saveContent($data)
    {
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        $this->db->insert('word_of_day_content', $data);
        return $this->db->insert_id();
    }

    /**
     * Update word of the day content
     */
    public function updateContent($id, $data)
    {
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        $this->db->where('id', $id);
        return $this->db->update('word_of_day_content', $data);
    }

    /**
     * Mark content as sent
     */
    public function markContentAsSent($content_id)
    {
        $data = [
            'is_sent' => 1,
            'sent_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        $this->db->where('id', $content_id);
        return $this->db->update('word_of_day_content', $data);
    }

    /**
     * Get active students for word of the day delivery
     */
    public function getActiveStudents($school_id)
    {
        $this->db->select('s.id as student_id, s.firstname, s.lastname, 
                          CONCAT(s.firstname, " ", s.lastname) as full_name, 
                          s.app_key, s.school_id');
        $this->db->from('students s');
        $this->db->join('student_session ss', 's.id = ss.student_id');
        $this->db->join('sessions sess', 'ss.session_id = sess.id');
        $this->db->where('s.school_id', $school_id);
        $this->db->where('s.is_active', 'yes'); 
        $this->db->where('ss.session_id', $this->current_session);
        // Removed app_key filter to include ALL active students
        $this->db->order_by('s.firstname, s.lastname');
        
        $query = $this->db->get(); 
        return $query->result();
    }

    /**
     * Create delivery log
     */
    public function createDeliveryLog($data)
    {
        $data['created_at'] = date('Y-m-d H:i:s');
        
        $this->db->insert('word_of_day_logs', $data);
        return $this->db->insert_id();
    }

    /**
     * Update delivery log
     */
    public function updateDeliveryLog($log_id, $data)
    {
        $this->db->where('id', $log_id);
        return $this->db->update('word_of_day_logs', $data);
    }

    /**
     * Save student delivery log
     */
    public function saveStudentLog($data)
    {
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        return $this->db->insert('word_of_day_student_logs', $data);
    }

    /**
     * Get delivery logs for a school
     */
    public function getDeliveryLogs($school_id, $limit = 30)
    {
        $this->db->select('wdl.*, wdc.title, wdc.content');
        $this->db->from('word_of_day_logs wdl');
        $this->db->join('word_of_day_content wdc', 'wdl.content_id = wdc.id');
        $this->db->where('wdl.school_id', $school_id);
        $this->db->order_by('wdl.delivery_date', 'DESC');
        $this->db->limit($limit);
        
        $query = $this->db->get();
        return $query->result();
    }

    /**
     * Get student delivery logs for a specific date
     */
    public function getStudentDeliveryLogs($school_id, $date, $limit = 100)
    {
        $this->db->select('wdsl.*, s.firstname, s.lastname, 
                          CONCAT(s.firstname, " ", s.lastname) as student_name');
        $this->db->from('word_of_day_student_logs wdsl');
        $this->db->join('students s', 'wdsl.student_id = s.id');
        $this->db->where('wdsl.school_id', $school_id);
        $this->db->where('wdsl.delivery_date', $date);
        $this->db->order_by('s.firstname, s.lastname');
        $this->db->limit($limit);
        
        $query = $this->db->get();
        return $query->result();
    }

    /**
     * Get content history for a school
     */
    public function getContentHistory($school_id, $limit = 30)
    {
        $this->db->where('school_id', $school_id);
        $this->db->order_by('content_date', 'DESC');
        $this->db->limit($limit);
        
        $query = $this->db->get('word_of_day_content');
        return $query->result();
    }

    /**
     * Get today's word of the day for all enabled schools
     */
    public function getTodaysWordForAllSchools()
    {
        $today = date('Y-m-d');
        
        $this->db->select('wdc.*, wds.delivery_time, ss.name as school_name, ss.timezone');
        $this->db->from('word_of_day_content wdc');
        $this->db->join('word_of_day_settings wds', 'wdc.school_id = wds.school_id');
        $this->db->join('sch_settings ss', 'wdc.school_id = ss.id');
        $this->db->where('wdc.content_date', $today);
        $this->db->where('wds.is_enabled', 1);
        $this->db->where('wdc.is_sent', 0);
        
        $query = $this->db->get();
        return $query->result();
    }

    /**
     * Get schools that need word of the day content generated
     */
    public function getSchoolsNeedingContent($date = null)
    {
        if ($date === null) {
            $date = date('Y-m-d');
        }
        
        $this->db->select('wds.school_id, ss.name as school_name, ss.timezone, wds.gemini_api_key');
        $this->db->from('word_of_day_settings wds');
        $this->db->join('sch_settings ss', 'wds.school_id = ss.id');
        $this->db->where('wds.is_enabled', 1);
        $this->db->where('wds.gemini_api_key IS NOT NULL');
        $this->db->where('wds.gemini_api_key !=', '');
        
        // Exclude schools that already have content for this date
        $this->db->where("wds.school_id NOT IN (
            SELECT school_id FROM word_of_day_content 
            WHERE content_date = '$date'
        )");
        
        $query = $this->db->get();
        return $query->result();
    }

    /**
     * Get delivery statistics for a school
     */
    public function getDeliveryStats($school_id, $days = 30)
    {
        $start_date = date('Y-m-d', strtotime("-$days days"));
        
        $this->db->select('
            COUNT(*) as total_deliveries,
            SUM(notice_sent) as total_notices_sent,
            SUM(push_sent) as total_push_sent,
            SUM(notice_failed) as total_notices_failed,
            SUM(push_failed) as total_push_failed,
            AVG(total_students) as avg_students
        ');
        $this->db->from('word_of_day_logs');
        $this->db->where('school_id', $school_id);
        $this->db->where('delivery_date >=', $start_date);
        
        $query = $this->db->get();
        return $query->row();
    }

    /**
     * Delete old logs (cleanup)
     */
    public function cleanupOldLogs($days = 90)
    {
        $cutoff_date = date('Y-m-d', strtotime("-$days days"));
        
        // Delete old student logs
        $this->db->where('delivery_date <', $cutoff_date);
        $this->db->delete('word_of_day_student_logs');
        
        // Delete old delivery logs
        $this->db->where('delivery_date <', $cutoff_date);
        $this->db->delete('word_of_day_logs');
        
        // Delete old content (keep content but remove old logs)
        // We might want to keep content for historical purposes
        
        return true;
    }

    /**
     * Check if content exists for date range
     */
    public function hasContentForDateRange($school_id, $start_date, $end_date)
    {
        $this->db->where('school_id', $school_id);
        $this->db->where('content_date >=', $start_date);
        $this->db->where('content_date <=', $end_date);
        
        $query = $this->db->get('word_of_day_content');
        return $query->num_rows();
    }

    /**
     * Get pending deliveries (for cron job)
     */
    public function getPendingDeliveries()
    {
        $current_time = date('H:i:s');
        $today = date('Y-m-d');
        
        $this->db->select('wds.school_id, wds.delivery_time, ss.name as school_name, ss.timezone');
        $this->db->from('word_of_day_settings wds');
        $this->db->join('sch_settings ss', 'wds.school_id = ss.id');
        $this->db->where('wds.is_enabled', 1);
        $this->db->where('wds.delivery_time <=', $current_time);
        
        // Only schools that haven't been delivered today
        $this->db->where("wds.school_id NOT IN (
            SELECT DISTINCT school_id FROM word_of_day_logs 
            WHERE delivery_date = '$today'
        )");
        
        $query = $this->db->get();
        return $query->result();
    }
}
