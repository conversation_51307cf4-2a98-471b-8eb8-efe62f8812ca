<link rel="stylesheet" href="<?php echo base_url(); ?>backend/plugins/bootstrap-wysihtml5/bootstrap3-wysihtml5.min.css">
<script src="<?php echo base_url(); ?>backend/plugins/bootstrap-wysihtml5/bootstrap3-wysihtml5.all.min.js"></script>

<div class="content-wrapper" style="min-height: 348px;">     
    <section class="content">
        <div class="row">
        
            <?php $this->load->view('setting/_settingmenu'); ?>
            
            <!-- left column -->
            <div class="col-md-10">
                <!-- general form elements -->

                <div class="box box-primary">
                    <div class="box-header ptbnull">
                        <h3 class="box-title titlefix"><i class="fa fa-gear"></i> <?php echo $this->lang->line('fees'); ?></h3>
                        <div class="box-tools pull-right">
                        </div><!-- /.box-tools -->
                    </div><!-- /.box-header -->
                    <div class="">
                        <form role="form" id="fees_form" method="post" enctype="multipart/form-data">
                            <input type="hidden" name="sch_id" value="<?php echo $result->id; ?>">
                            <div class="box-body">                       
                                <div class="row">
                                    <div class="row">
                                    <div class="col-md-12">
                                    <div class="col-md-12">
                                        <div class="form-group row">
                                            <label class="col-sm-4"><?php echo $this->lang->line("offline_bank_payment_in_student_panel"); ?></label>
                                            <div class="col-sm-8" id="radioBtnDiv">
                                                <label class="radio-inline">
                                                    <input type="radio" class="is_offline_fee_payment" name="is_offline_fee_payment" value="0" <?php
                                                    if ($result->is_offline_fee_payment == 0) {
                                                        echo "checked";
                                                    }
                                                    ?> ><?php echo $this->lang->line('disabled'); ?>
                                                </label>
                                                <label class="radio-inline">
                                                    <input type="radio" class="is_offline_fee_payment" name="is_offline_fee_payment" value="1"  <?php
                                                    if ($result->is_offline_fee_payment == 1) {
                                                        echo "checked";
                                                    }
                                                    ?>><?php echo $this->lang->line('enabled'); ?>
                                                </label>
                                            </div>
                                        </div>
                                    </div>                                    
                                    <div class="col-md-12">
                                        <div class="form-group row">
                                            <label class="col-sm-4"><?php echo $this->lang->line("offline_bank_payment_instruction"); ?></label>
                                            <div class="col-sm-8">                           
                                                
                                                <textarea id="offline_bank_payment_instruction" name="offline_bank_payment_instruction" class="form-control" style="height: 150px">
                                                <?php echo $result->offline_bank_payment_instruction ; ?>
                                                </textarea>
                                                <span class="text-danger"><?php echo form_error('offline_bank_payment_instruction'); ?></span>                                           
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="form-group row">
                                            <label class="col-sm-4">BANK ACCOUNT</label>
                                            <div class="col-sm-8">                           
                                                
                                                <textarea id="bank_account" name="bank_account" class="form-control" style="height: 150px">
                                                <?php echo $result->bank_account ; ?>
                                                </textarea>
                                                <span class="text-danger"><?php echo form_error('bank_account'); ?></span>                                           
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="form-group row">
                                            <label class="col-sm-4"> <?php echo $this->lang->line("lock_student_panel_if_fees_remaining"); ?></label>
                                            <div class="col-sm-8" id="radioBtnDiv">
                                                <label class="radio-inline">
                                                    <input type="radio" class="is_student_feature_lock" name="is_student_feature_lock" value="0" <?php
                                                    if ($result->is_student_feature_lock == 0) {
                                                        echo "checked";
                                                    }
                                                    ?> ><?php echo $this->lang->line('disabled'); ?>
                                                </label>
                                                <label class="radio-inline">
                                                    <input type="radio" class="is_student_feature_lock" name="is_student_feature_lock" value="1"  <?php
                                                    if ($result->is_student_feature_lock == 1) {
                                                        echo "checked";
                                                    }
                                                    ?>><?php echo $this->lang->line('enabled'); ?>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                        <div class="col-md-12 hide" id="fees_payment_grace_period">
                                            <div class="form-group row">
                                                <label class="col-sm-4"><?php echo $this->lang->line('fees_payment_grace_period'); ?><small class="req"> *</small></label>
                                                <div class="col-sm-8">
                                                    <input type="number" name="lock_grace_period" id="lock_grace_period" class="form-control" value="<?php echo $result->lock_grace_period; ?>">
                                                    <span class="text-danger"><?php echo form_error('lock_grace_period'); ?></span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="form-group row">
                                                <label class="col-sm-4"> <?php echo $this->lang->line("print_fees_receipt_for"); ?></label>
                                             
                                                <div class="col-sm-8">
                                                    <label class="checkbox-inline">
                                                    <input type="checkbox" name="is_duplicate_fees_invoice[]" value="0"  <?php echo set_checkbox("is_duplicate_fees_invoice[]", "0" ,(set_value('is_duplicate_fees_invoice[]', in_array(0, $duplicate_fees_invoice)) == 1) ? TRUE : FALSE) ?> ><?php echo $this->lang->line('office_copy'); ?>
                                                    </label>
                                                    <label class="checkbox-inline">
                                                        <input type="checkbox" name="is_duplicate_fees_invoice[]" value="1"  <?php echo set_checkbox("is_duplicate_fees_invoice[]", "1" ,(set_value('is_duplicate_fees_invoice[]', in_array(1, $duplicate_fees_invoice)) == 1) ? TRUE : FALSE) ?> ><?php echo $this->lang->line('student_copy'); ?>
                                                    </label>
                                                      <label class="checkbox-inline">
                                                        <input type="checkbox" name="is_duplicate_fees_invoice[]" value="2"  <?php echo set_checkbox("is_duplicate_fees_invoice[]", "2" ,(set_value('is_duplicate_fees_invoice[]', in_array(2, $duplicate_fees_invoice)) == 1) ? TRUE : FALSE) ?> ><?php echo $this->lang->line('bank_copy'); ?>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    </div>                                    
                                    <div class="row">
                                    <div class="col-md-12">
                                        <div class="col-md-12">
                                            <div class="form-group row">
                                                <label class="col-sm-4"><?php echo $this->lang->line('carry_forward_fees_due_days'); ?><small class="req"> *</small></label>
                                                <div class="col-sm-8">
                                                    <input type="number" name="fee_due_days" id="fee_due_days" class="form-control" value="<?php echo $result->fee_due_days; ?>">
                                                    <span class="text-danger"><?php echo form_error('fee_due_days'); ?></span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="form-group row">
                                                <label class="col-sm-4"><?php echo $this->lang->line('single_page_fees_print'); ?> </label>
                                                <div class="col-sm-8">
                                                    <label class="radio-inline">
                                                        <input type="radio" name="single_page_print" value="0"  <?php
                                                        if ($result->single_page_print == 0) {
                                                            echo "checked";
                                                        }
                                                        ?> ><?php echo $this->lang->line('disabled'); ?>
                                                    </label>
                                                    <label class="radio-inline">
                                                        <input type="radio" name="single_page_print" <?php
                                                        if ($result->single_page_print == 1) {
                                                            echo "checked";
                                                        }
                                                        ?> value="1"><?php echo $this->lang->line('enabled'); ?>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-12">
                                        <div class="col-md-12">
                                            <div class="form-group row">
                                                <label class="col-sm-4"> <?php echo $this->lang->line("collect_fees_in_back_date"); ?></label>
                                                <div class="col-sm-8">
                                                    <label class="radio-inline">
                                                        <input type="radio" name="collect_back_date_fees" value="0" <?php
                                                        if (!$result->collect_back_date_fees) {
                                                            echo "checked";
                                                        }
                                                        ?> ><?php echo $this->lang->line('disabled'); ?>
                                                    </label>
                                                    <label class="radio-inline">
                                                        <input type="radio" name="collect_back_date_fees" value="1" <?php
                                                        if ($result->collect_back_date_fees) {
                                                            echo "checked";
                                                        }
                                                        ?>><?php echo $this->lang->line('enabled'); ?>
                                                    </label>
                                                </div>   
                                            </div>
                                        </div>
                                        </div>
                                    </div>
                                    
                                </div><!--./row--> 
                            </div><!-- /.box-body -->
                            <div class="box-footer">
                                <?php
                                if ($this->rbac->hasPrivilege('general_setting', 'can_edit')) {
                                    ?>
                                    <button type="button" class="btn btn-primary submit_schsetting pull-right edit_fees" data-loading-text="<i class='fa fa-circle-o-notch fa-spin'></i> <?php echo $this->lang->line('processing'); ?>"> <?php echo $this->lang->line('save'); ?></button>
                                    <?php
                                }
                                ?>
                            </div>
                        </form>
                        <div class="row">
                                        <div class=" col-md-6 col-sm-6">
                                            <div class="card-body-logo">
                                                <h4>QR CODE IMAGE</h4>
                                                <div class="text-center"> 
                                                    <?php
                                                    if ($result->qr_code_img == "") {
                                                        ?>
                                                        <div class="card-body-logo-img"><img src="<?php echo $this->media_storage->getImageURL('uploads/school_content/qr_code/images.png'); ?>" class="" alt="" width="204" height="60"></div>
                                                        <?php
                                                    } else {
                                                        ?>
                                                        <div class="card-body-logo-img"><img src="<?php echo $this->media_storage->getImageURL('uploads/school_content/qr_code/'.$result->qr_code_img); ?>" class="" alt="" width="204" height="60"></div>
                                                        <?php
                                                    }
                                                    ?> 
                                                </div> 
                                                <a href="#schsetting" role="button" class="btn btn-primary btn-sm upload_admin_logo"><?php echo $this->lang->line('update'); ?></a>   
                                                <a href="#admin_logo" role="button" class="btn btn-primary btn-sm upload_admin_logo" style="display:none"> data-toggle="tooltip" title="<?php echo $this->lang->line('edit_admin_logo'); ?>" data-loading-text="<i class='fa fa-circle-o-notch fa-spin'></i> <?php echo $this->lang->line('processing'); ?>"><i class="fa fa-picture-o"></i> <?php echo $this->lang->line('edit_admin_logo'); ?></a>
                                            </div>
                                        </div>
                                    </div>
                    </div><!-- /.box-body -->
                </div>
            </div><!--/.col (left) -->
            <!-- right column -->
        </div>
    </section><!-- /.content -->
</div><!-- /.content-wrapper -->
<!-- new END -->
</div><!-- /.content-wrapper -->
<div class="modal fade" id="modal-upload_admin_logo" tabindex="-1" role="dialog" aria-labelledby="myModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel"><?php echo $this->lang->line('edit_admin_logo'); ?></h4>
            </div>
            <div class="modal-body upload_logo_body">
                <!-- ==== -->
                <form class="box_upload boxupload has-advanced-upload" method="post" action="<?php echo site_url('schsettings/ajax_editlogo') ?>" enctype="multipart/form-data">
                    <input value="<?php echo $result->id; ?>" type="hidden" name="id" id="id_logo_admin"/>
                    <input type="file" name="file" id="file_admin">
                    <!-- Drag and Drop container-->
                    <div class="box__input upload-admin_area"  id="uploadfile_admin">
                        <i class="fa fa-download box__icon"></i>
                        <label><strong><?php echo $this->lang->line('choose_a_file_or_drag_it_here'); ?></strong>
                            <!-- <span class="box__dragndrop"> <?php //echo $this->lang->line('or') ?> <span><?php //echo $this->lang->line('drag') ?></span><?php //echo $this->lang->line('it_here') ?></span> --></label>

                    </div>

                </form>
            </div>

        </div>
    </div>
</div>
<script type="text/javascript">
    var base_url = '<?php echo base_url(); ?>';
    var logo_type = "logo";
// set focus when modal is opened
    $('#modal-uploadfile').on('shown.bs.modal', function () {
        $('.upload_logo').button('reset');
    });

    $('.upload_admin_logo').on('click', function (e) {
        e.preventDefault();
        var $this = $(this);
        logo_type = $this.data('logo_type');
        $this.button('loading');
        $('#modal-upload_admin_logo').modal({
            show: true,
            backdrop: 'static',
            keyboard: false
        });
    });
// set focus when modal is opened
    $('#modal-upload_admin_logo').on('shown.bs.modal', function () {
        $('.upload_admin_logo').button('reset');
    });


// set focus when modal is opened


    $(".edit_setting").on('click', function (e) {
        var $this = $(this);
        $this.button('loading');
        $.ajax({
            url: '<?php echo site_url("schsettings/ajax_schedit") ?>',
            type: 'POST',
            data: $('#schsetting_form').serialize(),
            dataType: 'json',

            success: function (data) {

                if (data.status == "fail") {
                    var message = "";
                    $.each(data.error, function (index, value) {

                        message += value;
                    });
                    errorMsg(message);
                } else {
                    successMsg(data.message);
                    window.location.reload(true);
                }

                $this.button('reset');
            }
        });
    });
</script>
<script type="text/javascript">
    $(function () {

        // Drag enter
        $('.upload-area').on('dragenter', function (e) {
            e.stopPropagation();
            e.preventDefault();
            $("h1").text("Drop");
        });

        // Drag over
        $('.upload-area').on('dragover', function (e) {
            e.stopPropagation();
            e.preventDefault();
            $("h1").text("Drop");
        });

        // Drop
        $('.upload-area').on('drop', function (e) {
            e.stopPropagation();
            e.preventDefault();

            $("h1").text("Upload");

            var file = e.originalEvent.dataTransfer.files;
            var fd = new FormData();

            fd.append('file', file[0]);
            fd.append("id", $('#id_logo').val());
            fd.append("logo_type", logo_type);

            uploadData(fd);
        });

        // Open file selector on div click
        $("#uploadfile").click(function () {
            $("#file").click();
        });

        // file selected
        $("#file").change(function () {
            var fd = new FormData();

            var files = $('#file')[0].files[0];

            fd.append('file', files);
            fd.append("id", $('#id_logo').val());
            fd.append("logo_type", logo_type);
            uploadData(fd);
        });
    });

// Sending AJAX request and upload file
    function uploadData(formdata) {

        $.ajax({
            url: '<?php echo site_url('schsettings/ajax_editlogo') ?>',
            type: 'post',
            data: formdata,
            contentType: false,
            processData: false,
            dataType: 'json',
            cache: false,

            beforeSend: function () {
                $('#modal-uploadfile').addClass('modal_loading');
            },
            success: function (response) {
                if (response.success) {
                    successMsg(response.message);
                    window.location.reload(true);
                } else {

                    errorMsg(response.error.file);
                }

            },
            error: function (xhr) { // if error occured

            },
            complete: function () {
                $('#modal-uploadfile').removeClass('modal_loading');

            }


        });
    }

    $(function () {

        // Drag enter
        $('.upload-small_area').on('dragenter', function (e) {
            e.stopPropagation();
            e.preventDefault();
            $("h1").text("Drop");
        });

        // Drag over
        $('.upload-small_area').on('dragover', function (e) {
            e.stopPropagation();
            e.preventDefault();
            $("h1").text("Drop");
        });

        // Drop
        $('.upload-small_area').on('drop', function (e) {
            e.stopPropagation();
            e.preventDefault();

            $("h1").text("Upload");

            var file = e.originalEvent.dataTransfer.files;
            var fd = new FormData();

            fd.append('file', file[0]);
            fd.append("id", $('#id_logo_small').val());
            fd.append("logo_type", logo_type);

            uploadSmallData(fd);
        });

        // Open file selector on div click
        $("#uploadfile_small").click(function () {
            $("#file_small").click();
        });

        // file selected
        $("#file_small").change(function () {
            var fd = new FormData();

            var files = $('#file_small')[0].files[0];

            fd.append('file', files);
            fd.append("id", $('#id_logo_small').val());
            fd.append("logo_type", logo_type);
            uploadSmallData(fd);
        });
    });

// Sending AJAX request and upload file
    function uploadSmallData(formdata) {

        $.ajax({
            url: '<?php echo site_url('schsettings/ajax_editadmin_qrcode') ?>',
            type: 'post',
            data: formdata,
            contentType: false,
            processData: false,
            dataType: 'json',
            cache: false,

            beforeSend: function () {
                $('#modal-upload_admin_small_logo').addClass('modal_loading');
            },
            success: function (response) {

                if (response.success) {
                    successMsg(response.message);
                    window.location.reload(true);
                } else {

                    errorMsg(response.error.file);
                }

            },
            error: function (xhr) { // if error occured

            },
            complete: function () {
                $('#modal-upload_admin_small_logo').removeClass('modal_loading');

            }


        });
    }

    $(function () {

        // Drag enter
        $('.upload-admin_area').on('dragenter', function (e) {
            e.stopPropagation();
            e.preventDefault();
            $("h1").text("Drop");
        });

        // Drag over
        $('.upload-admin_area').on('dragover', function (e) {
            e.stopPropagation();
            e.preventDefault();
            $("h1").text("Drop");
        });

        // Drop
        $('.upload-admin_area').on('drop', function (e) {
            e.stopPropagation();
            e.preventDefault();

            $("h1").text("Upload");

            var file = e.originalEvent.dataTransfer.files;
            var fd = new FormData();

            fd.append('file', file[0]);
            fd.append("id", $('#id_logo_small').val());
            fd.append("logo_type", logo_type);

            uploadadminlData(fd);
        });

        // Open file selector on div click
        $("#uploadfile_admin").click(function () {
            $("#file_admin").click();
        });

        // file selected
        $("#file_admin").change(function () {
            var fd = new FormData();

            var files = $('#file_admin')[0].files[0];

            fd.append('file', files);
            fd.append("id", $('#id_logo_small').val());
            fd.append("logo_type", logo_type);
            uploadadminData(fd);
        });
    });

// Sending AJAX request and upload file
    function uploadadminData(formdata) {

        $.ajax({
            url: '<?php echo site_url('schsettings/ajax_editadmin_qrcode') ?>',
            type: 'post',
            data: formdata,
            contentType: false,
            processData: false,
            dataType: 'json',
            cache: false,

            beforeSend: function () {
                $('#modal-upload_admin_logo').addClass('modal_loading');
            },
            success: function (response) {

                if (response.success) {
                    successMsg(response.message);
                    window.location.reload(true);
                } else {

                    errorMsg(response.error.file);
                }

            },
            error: function (xhr) { // if error occured

            },
            complete: function () {
                $('#modal-upload_admin_logo').removeClass('modal_loading');

            }


        });
    }

</script>


<script type="text/javascript">
    $('.upload_app_logo').on('click', function (e) {
        e.preventDefault();
        var $this = $(this);
        logo_type = $this.data('logo_type');

        $this.button('loading');
        $('#modal-upload_app_logo').modal({
            show: true,
            backdrop: 'static',
            keyboard: false
        });
    });
// set focus when modal is opened
    $('#modal-upload_app_logo').on('shown.bs.modal', function () {
        $('.upload_app_logo').button('reset');
    });

    $(function () {

        // Drag enter
        $('.upload-app_logo_area').on('dragenter', function (e) {
            e.stopPropagation();
            e.preventDefault();
            $("h1").text("Drop");
        });

        // Drag over
        $('.upload-app_logo_area').on('dragover', function (e) {
            e.stopPropagation();
            e.preventDefault();
            $("h1").text("Drop");
        });

        // Drop
        $('.upload-app_logo_area').on('drop', function (e) {
            e.stopPropagation();
            e.preventDefault();

            $("h1").text("Upload");

            var file = e.originalEvent.dataTransfer.files;
            var fd = new FormData();

            fd.append('file', file[0]);
            fd.append("id", $('#id_app_logo').val());
            // fd.append("logo_type", logo_type);

            uploadSmallData(fd);
        });

        // Open file selector on div click
        $("#uploadapp_logo").click(function () {
            $("#file_applogo").click();
        });

        // file selected
        $("#file_applogo").change(function () {
            var fd = new FormData();

            var files = $('#file_applogo')[0].files[0];


            fd.append('file', files);
            fd.append("id", $('#id_app_logo').val());
            // fd.append("logo_type", logo_type);
            uploadAppData(fd);
        });
    });

// Sending AJAX request and upload file
    function uploadAppData(formdata) {

        $.ajax({
            url: '<?php echo site_url('schsettings/ajax_applogo') ?>',
            type: 'post',
            data: formdata,
            contentType: false,
            processData: false,
            dataType: 'json',
            cache: false,

            beforeSend: function () {
                $('#modal-upload_app_logo').addClass('modal_loading');
            },
            success: function (response) {

                if (response.success) {
                    successMsg(response.message);
                    window.location.reload(true);
                } else {

                    errorMsg(response.error.file);
                }

            },
            error: function (xhr) { // if error occured

            },
            complete: function () {
                $('#modal-upload_app_logo').removeClass('modal_loading');

            }


        });
    }
</script>
<script type="text/javascript">
    var base_url = '<?php echo base_url(); ?>';
 
    $(".edit_fees").on('click', function (e) {
        var $this = $(this);
        $this.button('loading');
        $.ajax({
            url: '<?php echo site_url("schsettings/savefees") ?>',
            type: 'POST',
            data: $('#fees_form').serialize(),
            dataType: 'json',

            success: function (data) {

                if (data.status == "fail") {
                    var message = "";
                    $.each(data.error, function (index, value) {

                        message += value;
                    });
                    errorMsg(message);
                } else {
                    successMsg(data.message); 
                }
                $this.button('reset');
            }
        });
    });
</script>


<script type="text/javascript">
     $('input[type=radio][name=is_student_feature_lock]').change(function() {
        if (this.value == '1') {
            $('#fees_payment_grace_period').removeClass('hide'); 
        }
        else if (this.value == '0') {
             $('#fees_payment_grace_period').addClass('hide');   
        }
    }); 
     
    window.onload = function(){  
        var is_student_feature_lock = '<?php echo $result->is_student_feature_lock; ?>';  
        if(is_student_feature_lock == '1'){
            $('#fees_payment_grace_period').removeClass('hide'); 
        }else if(is_student_feature_lock == '0'){
            $('#fees_payment_grace_period').addClass('hide');   
        }
    }  
</script>   
<script>
    $(function () {
        $("#offline_bank_payment_instruction").wysihtml5();
    });
    $(function () {
        $("#bank_account").wysihtml5();
    });
</script>