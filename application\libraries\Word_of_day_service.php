<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Word of the Day Service Library
 * 
 * This library handles:
 * - Fetching inspirational content from Gemini API
 * - Sending word of the day to students via notices and push notifications
 * - Managing word of the day settings and content
 * - Logging delivery attempts and results
 */
class Word_of_day_service
{
    protected $CI;
    
    public function __construct()
    {
        $this->CI =& get_instance();
        $this->CI->load->model('word_of_day_model');
        $this->CI->load->library('pushnotification');
        $this->CI->load->database();
        
        // Set timezone for proper date handling
        if (function_exists('date_default_timezone_set')) {
            $setting = $this->CI->db->get_where('sch_settings', ['id' => $this->CI->session->userdata('school_id')])->row();
            if ($setting && !empty($setting->timezone)) {
                date_default_timezone_set($setting->timezone);
            }
        }
    }
    
    /**
     * Generate word of the day content using Gemini API
     */
    public function generateWordOfDay($school_id, $date = null)
    {
        if ($date === null) {
            $date = date('Y-m-d');
        }
        
        // Get school settings
        $settings = $this->getSettings($school_id);
        if (!$settings || !$settings->is_enabled || empty($settings->gemini_api_key)) {
            return [
                'success' => false,
                'error' => 'Word of the day is not enabled or Gemini API key not configured'
            ];
        }
        
        // Check if content already exists for this date
        $existing = $this->CI->word_of_day_model->getContentByDate($school_id, $date);
        if ($existing) {
            return [
                'success' => true,
                'content' => $existing,
                'message' => 'Content already exists for this date'
            ];
        }
        
        // Get school info for personalization
        $school = $this->CI->db->get_where('sch_settings', ['id' => $school_id])->row();
        $school_name = $school ? $school->name : 'School';
        
        // Generate content using Gemini API
        $prompt = $this->buildGeminiPrompt($school_name, $date);
        $gemini_response = $this->callGeminiAPI($settings->gemini_api_key, $prompt);
        
        if (!$gemini_response['success']) {
            return $gemini_response;
        }
        
        // Parse and save the content
        $parsed_content = $this->parseGeminiResponse($gemini_response['data']);
        
        $content_data = [
            'school_id' => $school_id,
            'content_date' => $date,
            'title' => $parsed_content['title'],
            'content' => $parsed_content['content'],
            'source' => 'gemini',
            'gemini_prompt' => $prompt,
            'gemini_response' => json_encode($gemini_response['data'])
        ];
        
        $content_id = $this->CI->word_of_day_model->saveContent($content_data);
        
        if ($content_id) {
            $content_data['id'] = $content_id;
            return [
                'success' => true,
                'content' => (object) $content_data,
                'message' => 'Word of the day generated successfully'
            ];
        } else {
            return [
                'success' => false,
                'error' => 'Failed to save generated content'
            ];
        }
    }
    
    /**
     * Send word of the day to all students
     */
    public function sendWordOfDay($school_id, $date = null)
    {
        if ($date === null) {
            $date = date('Y-m-d');
        }
        
        // Get settings
        $settings = $this->getSettings($school_id);
        if (!$settings || !$settings->is_enabled) {
            return [
                'success' => false,
                'error' => 'Word of the day is not enabled for this school'
            ];
        }
        
        // Get content for the date
        $content = $this->CI->word_of_day_model->getContentByDate($school_id, $date);
        if (!$content) {
            // Try to generate content first
            $generate_result = $this->generateWordOfDay($school_id, $date);
            if (!$generate_result['success']) {
                return $generate_result;
            }
            $content = $generate_result['content'];
        }
        
        // Check if already sent
        if ($content->is_sent) {
            return [
                'success' => false,
                'error' => 'Word of the day already sent for this date'
            ];
        }
        
        // Get all active students
        $students = $this->CI->word_of_day_model->getActiveStudents($school_id);
        if (empty($students)) {
            return [
                'success' => false,
                'error' => 'No active students found'
            ];
        }
        
        // Initialize delivery log
        $log_data = [
            'school_id' => $school_id,
            'content_id' => $content->id,
            'delivery_date' => $date,
            'total_students' => count($students),
            'started_at' => date('Y-m-d H:i:s')
        ];
        $log_id = $this->CI->word_of_day_model->createDeliveryLog($log_data);
        
        $notice_sent = 0;
        $push_sent = 0;
        $notice_failed = 0;
        $push_failed = 0;
        
        // Send to each student
        foreach ($students as $student) {
            $student_log = [
                'school_id' => $school_id,
                'content_id' => $content->id,
                'student_id' => $student->student_id,
                'delivery_date' => $date,
                'app_key' => $student->app_key
            ];
            
            // Send notice
            if ($settings->send_notice) {
                $notice_result = $this->sendNotice($student, $content, $settings);
                if ($notice_result['success']) {
                    $student_log['notice_id'] = $notice_result['notice_id'];
                    $student_log['notice_status'] = 'sent';
                    $notice_sent++;
                } else {
                    $student_log['notice_status'] = 'failed';
                    $student_log['error_message'] = $notice_result['error'];
                    $notice_failed++;
                }
            } else {
                $student_log['notice_status'] = 'skipped';
            }
            
            // Send push notification
            if ($settings->send_push) {
                if (!empty($student->app_key)) {
                    $push_result = $this->sendPushNotification($student, $content, $settings);
                    if ($push_result['success']) {
                        $student_log['push_status'] = 'sent';
                        $student_log['push_response'] = json_encode($push_result['response']);
                        $push_sent++;
                    } else {
                        $student_log['push_status'] = 'failed';
                        $student_log['error_message'] = $push_result['error'];
                        $push_failed++;
                    }
                } else {
                    // Skip push for students without app_key
                    $student_log['push_status'] = 'no_app_key';
                }
            } else {
                $student_log['push_status'] = 'skipped';
            }
            
            // Save student log
            $this->CI->word_of_day_model->saveStudentLog($student_log);
        }
        
        // Update delivery log
        $log_update = [
            'notice_sent' => $notice_sent,
            'push_sent' => $push_sent,
            'notice_failed' => $notice_failed,
            'push_failed' => $push_failed,
            'notice_status' => $notice_sent > 0 ? 'sent' : ($notice_failed > 0 ? 'failed' : 'skipped'),
            'push_status' => $push_sent > 0 ? 'sent' : ($push_failed > 0 ? 'failed' : 'skipped'),
            'completed_at' => date('Y-m-d H:i:s')
        ];
        $this->CI->word_of_day_model->updateDeliveryLog($log_id, $log_update);
        
        // Mark content as sent
        $this->CI->word_of_day_model->markContentAsSent($content->id);
        
        // Count students without app keys
        $students_without_app_key = 0;
        foreach ($students as $student) {
            if (empty($student->app_key)) {
                $students_without_app_key++;
            }
        }
        
        return [
            'success' => true,
            'message' => 'Word of the day sent successfully',
            'stats' => [
                'total_students' => count($students),
                'students_without_app_key' => $students_without_app_key,
                'notice_sent' => $notice_sent,
                'push_sent' => $push_sent,
                'notice_failed' => $notice_failed,
                'push_failed' => $push_failed
            ]
        ];
    }
    
    /**
     * Build prompt for Gemini API
     */
    private function buildGeminiPrompt($school_name, $date)
    {
        $day_name = date('l', strtotime($date));
        $formatted_date = date('F j, Y', strtotime($date));
        
        return "Generate an inspirational and motivational 'Word of the Day' for students at {$school_name} for {$day_name}, {$formatted_date}.

Please provide:
1. A short, catchy title (2-4 words)
2. An inspirational message (50-100 words) that is:
   - Age-appropriate for school students
   - Motivational and positive
   - Educational or thought-provoking
   - Relevant to student life and learning

Format your response using HTML tags with the following structure:

<title>Your Title Here</title>
<content>Your inspirational message here. You can use HTML tags like <strong>bold text</strong>, <em>emphasis</em>, <br> for line breaks, and other basic HTML formatting.</content>

Example format:
<title>Believe in Yourself</title>
<content>Every great achievement starts with the belief that you can do it. Today, remember that your potential is <strong>limitless</strong>. Every challenge you face is an opportunity to grow stronger and wiser.<br><br>Don't let fear of failure stop you from trying. Success comes to those who dare to begin. <em>Believe in yourself, work hard, and amazing things will happen!</em></content>";
    }
    
    /**
     * Call Gemini API
     */
    private function callGeminiAPI($api_key, $prompt)
    {
        $url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=" . $api_key;
        
        $data = [
            'contents' => [
                [
                    'parts' => [
                        ['text' => $prompt]
                    ]
                ]
            ]
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            return [
                'success' => false,
                'error' => 'CURL Error: ' . $error
            ];
        }
        
        if ($http_code !== 200) {
            return [
                'success' => false,
                'error' => 'HTTP Error: ' . $http_code . ' - ' . $response
            ];
        }
        
        $decoded = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return [
                'success' => false,
                'error' => 'Invalid JSON response from Gemini API'
            ];
        }
        
        return [
            'success' => true,
            'data' => $decoded
        ];
    }
    
    /**
     * Parse Gemini API response
     */
    private function parseGeminiResponse($response)
    {
        try {
            if (isset($response['candidates'][0]['content']['parts'][0]['text'])) {
                $text = $response['candidates'][0]['content']['parts'][0]['text'];

                // Try to parse HTML format first
                $title = "Word of the Day";
                $content = $text;

                // Extract title from <title> tags
                if (preg_match('/<title>(.*?)<\/title>/is', $text, $title_matches)) {
                    $title = trim(strip_tags($title_matches[1]));
                }

                // Extract content from <content> tags
                if (preg_match('/<content>(.*?)<\/content>/is', $text, $content_matches)) {
                    $content = trim($content_matches[1]);
                } else {
                    // Fallback: try to parse as JSON (for backward compatibility)
                    $json_data = json_decode($text, true);
                    if (json_last_error() === JSON_ERROR_NONE && isset($json_data['title']) && isset($json_data['content'])) {
                        return [
                            'title' => trim($json_data['title']),
                            'content' => trim($json_data['content'])
                        ];
                    }

                    // Fallback: extract title and content from text
                    $lines = explode("\n", $text);

                    // Look for title patterns
                    foreach ($lines as $line) {
                        $line = trim($line);
                        if (preg_match('/^(title|heading):\s*(.+)/i', $line, $matches)) {
                            $title = trim($matches[2], '"\'');
                            break;
                        } elseif (preg_match('/^"([^"]+)"/', $line, $matches)) {
                            $title = $matches[1];
                            break;
                        }
                    }

                    $content = $text;
                }

                return [
                    'title' => $title,
                    'content' => trim($content)
                ];
            }
        } catch (Exception $e) {
            // Fallback content
        }

        // Default fallback
        return [
            'title' => 'Stay Motivated',
            'content' => 'Every day is a new opportunity to learn, grow, and achieve your dreams. <strong>Believe in yourself</strong> and keep moving forward!'
        ];
    }
    
    /**
     * Get word of the day settings for a school
     */
    public function getSettings($school_id)
    {
        return $this->CI->word_of_day_model->getSettings($school_id);
    }
    
    /**
     * Send notice to student
     */
    private function sendNotice($student, $content, $settings)
    {
        try {
            // Get school info
            $school = $this->CI->db->get_where('sch_settings', ['id' => $student->school_id])->row();
            
            // Replace template variables
            $message = str_replace(
                ['{title}', '{content}', '{school_name}', '{student_name}'],
                [$content->title, $content->content, $school->name, $student->full_name],
                $settings->notice_template
            );
            
            // Create notice
            $notice_data = [
                'title' => '🌟 ' . $content->title,
                'message' => $message,
                'publish_date' => date('Y-m-d'),
                'date' => date('Y-m-d H:i:s'),
                'visible_student' => 'Yes',
                'visible_staff' => 'No',
                'visible_parent' => 'No',
                'created_by' => 'Word of Day System',
                'is_active' => 'yes',
                'created_id' => 1, // System generated
                'school_id' => $student->school_id
            ];
            
            $this->CI->db->insert('send_notification', $notice_data);
            $notice_id = $this->CI->db->insert_id();
            
            if ($notice_id) {
                return [
                    'success' => true,
                    'notice_id' => $notice_id
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'Failed to create notice'
                ];
            }
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'Exception: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Send push notification to student
     */
    private function sendPushNotification($student, $content, $settings)
    {
        try {
            // Validate app_key
            if (empty($student->app_key)) {
                return [
                    'success' => false,
                    'error' => 'Student app_key is empty'
                ];
            }

            // Get school info
            $school = $this->CI->db->get_where('sch_settings', ['id' => $student->school_id])->row();

            // Replace template variables
            $message = str_replace(
                ['{title}', '{content}', '{school_name}', '{student_name}'],
                [$content->title, $content->content, $school->name, $student->full_name],
                $settings->push_template
            );

            // Prepare push notification data
            $push_data = [
                'title' => '🌟 Word of the Day',
                'body' => $message
            ];

            // Send push notification using existing library
            $result = $this->CI->pushnotification->send($student->app_key, $push_data, "word_of_day");

            // Check if result indicates success
            if ($result !== false && !empty($result)) {
                return [
                    'success' => true,
                    'response' => $result
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'Push notification service returned false or empty result'
                ];
            }

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'Exception: ' . $e->getMessage()
            ];
        }
    }
}
