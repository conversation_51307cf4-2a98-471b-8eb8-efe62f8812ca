<div class="content-wrapper" style="min-height: 946px;">
    <section class="content-header">
        <h1>
            <i class="fa fa-calendar"></i> Word of the Day Management <small>Manage daily inspirational messages</small>
        </h1>
        <ol class="breadcrumb">
            <li><a href="<?php echo base_url(); ?>admin/admin/dashboard"><i class="fa fa-dashboard"></i> Home</a></li>
            <li><a href="<?php echo base_url(); ?>schsettings">General Settings</a></li>
            <li class="active">Word of the Day</li>
        </ol>
    </section>

    <section class="content">
        <div class="row">
            <div class="col-md-12">
                <?php echo $this->session->flashdata('msg'); ?>
                
                <!-- Settings Box -->
                <div class="box box-primary">
                    <div class="box-header with-border">
                        <h3 class="box-title"><i class="fa fa-cog"></i> Word of the Day Settings</h3>
                    </div>
                    
                    <form action="<?php echo base_url(); ?>schsettings/savewordofday" method="post">
                        <div class="box-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>
                                            <input type="checkbox" name="is_enabled" value="1" 
                                                   <?php echo ($settings && $settings->is_enabled) ? 'checked' : ''; ?>>
                                            Enable Word of the Day
                                        </label>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="delivery_time">Delivery Time</label>
                                        <input type="time" class="form-control" name="delivery_time" 
                                               value="<?php echo $settings ? $settings->delivery_time : '06:00:00'; ?>">
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label>
                                            <input type="checkbox" name="send_notice" value="1" 
                                                   <?php echo ($settings && $settings->send_notice) ? 'checked' : ''; ?>>
                                            Send User Notices
                                        </label>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label>
                                            <input type="checkbox" name="send_push" value="1" 
                                                   <?php echo ($settings && $settings->send_push) ? 'checked' : ''; ?>>
                                            Send Push Notifications
                                        </label>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="notice_template">Notice Template</label>
                                        <textarea class="form-control" name="notice_template" rows="4" placeholder="Template for user notices"><?php echo $settings ? $settings->notice_template : '🌟 <strong>Word of the Day</strong> 🌟<br><br><strong>{title}</strong><br><br>{content}<br><br>Have a wonderful day ahead!<br><br>- {school_name}'; ?></textarea>
                                        <small class="text-muted">Available variables: {title}, {content}, {school_name}, {student_name}. You can use HTML tags like &lt;strong&gt;, &lt;em&gt;, &lt;br&gt; for formatting.</small>
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="push_template">Push Notification Template</label>
                                        <textarea class="form-control" name="push_template" rows="4" placeholder="Template for push notifications"><?php echo $settings ? $settings->push_template : '🌟 {title} - {content}'; ?></textarea>
                                        <small class="text-muted">Available variables: {title}, {content}, {school_name}, {student_name}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="box-footer">
                            <button type="submit" class="btn btn-primary">
                                <i class="fa fa-save"></i> Save Settings
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Actions Box -->
                <div class="box box-success">
                    <div class="box-header with-border">
                        <h3 class="box-title"><i class="fa fa-play"></i> Manual Actions</h3>
                    </div>
                    <div class="box-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p>Generate today's word of the day content using Gemini AI:</p>
                                <a href="<?php echo base_url(); ?>schsettings/testwordofday" class="btn btn-success">
                                    <i class="fa fa-magic"></i> Generate Today's Word
                                </a>
                            </div>
                            <div class="col-md-6">
                                <p>Send today's word of the day to all students:</p>
                                <a href="<?php echo base_url(); ?>schsettings/sendwordofday" class="btn btn-warning" 
                                   onclick="return confirm('Are you sure you want to send word of the day to all students?')">
                                    <i class="fa fa-send"></i> Send to Students
                                </a>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistics Box -->
                <?php if ($stats): ?>
                <div class="box box-info">
                    <div class="box-header with-border">
                        <h3 class="box-title"><i class="fa fa-bar-chart"></i> Delivery Statistics (Last 30 Days)</h3>
                    </div>
                    <div class="box-body">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="info-box bg-aqua">
                                    <span class="info-box-icon"><i class="fa fa-calendar"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Total Deliveries</span>
                                        <span class="info-box-number"><?php echo $stats->total_deliveries ?? 0; ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="info-box bg-green">
                                    <span class="info-box-icon"><i class="fa fa-bell"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Notices Sent</span>
                                        <span class="info-box-number"><?php echo $stats->total_notices_sent ?? 0; ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="info-box bg-yellow">
                                    <span class="info-box-icon"><i class="fa fa-mobile"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Push Sent</span>
                                        <span class="info-box-number"><?php echo $stats->total_push_sent ?? 0; ?></span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="info-box bg-blue">
                                    <span class="info-box-icon"><i class="fa fa-users"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Avg Students</span>
                                        <span class="info-box-number"><?php echo round($stats->avg_students ?? 0); ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Recent Content Box -->
                <?php if (!empty($content_history)): ?>
                <div class="box box-default">
                    <div class="box-header with-border">
                        <h3 class="box-title"><i class="fa fa-history"></i> Recent Content</h3>
                    </div>
                    <div class="box-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Title</th>
                                        <th>Content Preview</th>
                                        <th>Source</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($content_history as $content): ?>
                                    <tr>
                                        <td><?php echo date('M j, Y', strtotime($content->content_date)); ?></td>
                                        <td><strong><?php echo htmlspecialchars($content->title); ?></strong></td>
                                        <td><?php echo substr(htmlspecialchars($content->content), 0, 100) . '...'; ?></td>
                                        <td>
                                            <span class="label label-<?php echo $content->source == 'gemini' ? 'primary' : 'default'; ?>">
                                                <?php echo ucfirst($content->source); ?>
                                            </span>
                                        </td>
                                        <td>
                                            <span class="label label-<?php echo $content->is_sent ? 'success' : 'warning'; ?>">
                                                <?php echo $content->is_sent ? 'Sent' : 'Pending'; ?>
                                            </span>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <?php endif; ?>

                <!-- Recent Delivery Logs -->
                <?php if (!empty($delivery_logs)): ?>
                <div class="box box-warning">
                    <div class="box-header with-border">
                        <h3 class="box-title"><i class="fa fa-list"></i> Recent Delivery Logs</h3>
                    </div>
                    <div class="box-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Title</th>
                                        <th>Students</th>
                                        <th>Notices</th>
                                        <th>Push</th>
                                        <th>Status</th>
                                        <th>Completed</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($delivery_logs as $log): ?>
                                    <tr>
                                        <td><?php echo date('M j, Y', strtotime($log->delivery_date)); ?></td>
                                        <td><?php echo htmlspecialchars($log->title); ?></td>
                                        <td><?php echo $log->total_students; ?></td>
                                        <td>
                                            <span class="text-success"><?php echo $log->notice_sent; ?></span>
                                            <?php if ($log->notice_failed > 0): ?>
                                                / <span class="text-danger"><?php echo $log->notice_failed; ?> failed</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="text-success"><?php echo $log->push_sent; ?></span>
                                            <?php if ($log->push_failed > 0): ?>
                                                / <span class="text-danger"><?php echo $log->push_failed; ?> failed</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="label label-<?php echo ($log->notice_status == 'sent' && $log->push_status == 'sent') ? 'success' : 'warning'; ?>">
                                                <?php echo ($log->notice_status == 'sent' && $log->push_status == 'sent') ? 'Complete' : 'Partial'; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($log->completed_at): ?>
                                                <?php echo date('H:i', strtotime($log->completed_at)); ?>
                                            <?php else: ?>
                                                <span class="text-muted">In Progress</span>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </section>
</div>
